-- Entity type (e.g. task, note, etc.)
CREATE TABLE types (
  id TEXT PRIMARY KEY,
  label TEXT
);

INSERT INTO types
(id) VALUES
('task'),
('note');

CREATE TABLE fields (
  id TEXT PRIMARY KEY, -- 'title', 'status', etc.
  type_id TEXT NOT NULL, -- FK to types.id
  field_type TEXT NOT NULL, -- 'text', 'number', 'date', etc.
  required INTEGER DEFAULT 0, -- 1 if required
  FOREIGN KEY (type_id) REFERENCES types(id)
);

INSERT INTO fields
(id,         type_id,   field_type,  required) VALUES
('title',   'task',     'text',      1),
('done',    'task',     'boolean',   0),
('due',     'task',     'date',      0);


-- Entities
CREATE TABLE entities (
  id TEXT PRIMARY KEY, -- UUID
  type_id TEXT NOT NULL, -- FK to types.id
  created_at INTEGER NOT NULL, -- UTC timestamp
  FOREIGN KEY (type_id) REFERENCES types(id)
);

-- Events
-- Events are used to modify entity fields
CREATE TABLE events (
  id TEXT PRIMARY KEY, -- UUID
  entity_id TEXT NOT NULL, -- FK to entities.id
  field_id TEXT NOT NULL, -- FK to fields.id
  value TEXT, -- new value (can be NULL)
  ts INTEGER NOT NULL, -- UTC timestamp
  node TEXT NOT NULL, -- origin node
  FOREIGN KEY (entity_id) REFERENCES entities(id),
  FOREIGN KEY (field_id) REFERENCES fields(id)
);

-- Estado actual de cada campo (opcional, para lecturas rápidas)
CREATE TABLE snapshots (
  entity_id TEXT NOT NULL,
  field_id TEXT NOT NULL,
  value TEXT,
  ts INTEGER,
  PRIMARY KEY (entity_id, field_id),
  FOREIGN KEY (entity_id) REFERENCES entities(id),
  FOREIGN KEY (field_id) REFERENCES fields(id)
);

-- Registro local de acciones o errores, no se sincroniza
CREATE TABLE logs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  level TEXT NOT NULL,         -- 'info', 'warn', 'error', etc.
  message TEXT NOT NULL,
  context TEXT,                -- JSON con metadata opcional
  created_at INTEGER NOT NULL  -- timestamp UTC
);
