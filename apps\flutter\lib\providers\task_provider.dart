import 'package:flutter/foundation.dart';
import 'package:core/core.dart';
import 'package:uuid/uuid.dart';

import '../models/task.dart';

/// Provider for managing task state in Flutter
class TaskProvider extends ChangeNotifier {
  final Core _core;
  final List<Task> _tasks = [];
  bool _isLoading = false;
  String? _error;
  
  TaskProvider(this._core) {
    _initialize();
  }
  
  /// Get the list of tasks
  List<Task> get tasks => List.unmodifiable(_tasks);
  
  /// Get loading state
  bool get isLoading => _isLoading;
  
  /// Get error message
  String? get error => _error;
  
  /// Get node ID
  String get nodeId => _core.nodeId;

  /// Get known nodes count
  int get knownNodesCount => _core.sync.knownNodes.length;
  
  /// Initialize the provider
  Future<void> _initialize() async {
    await _loadTasks();
    
    // Listen to new events from the event store
    // Note: This would need to be implemented in the EventStore
    // _driftCore.database.eventStream.listen(_onNewEvent);
  }
  
  /// Load tasks from the database
  Future<void> _loadTasks() async {
    _setLoading(true);
    _setError(null);

    try {
      final db = _core.database.database;

      // Get all task entities
      final entityResults = await db.query(
        'entities',
        where: 'type_id = ?',
        whereArgs: ['task'],
        orderBy: 'created_at ASC',
      );

      _tasks.clear();

      for (final entityRow in entityResults) {
        final entityId = entityRow['id'] as String;
        final createdAt = DateTime.fromMillisecondsSinceEpoch(entityRow['created_at'] as int);

        // Get all events for this entity
        final eventResults = await db.query(
          'events',
          where: 'entity_id = ?',
          whereArgs: [entityId],
          orderBy: 'ts DESC', // Most recent first
        );

        // Reconstruct task fields from events
        final fields = <String, String?>{};
        final processedFields = <String>{};

        for (final eventRow in eventResults) {
          final fieldId = eventRow['field_id'] as String;

          // Only use the most recent event for each field
          if (!processedFields.contains(fieldId)) {
            fields[fieldId] = eventRow['value'] as String?;
            processedFields.add(fieldId);
          }
        }

        // Create task from reconstructed fields
        final task = Task.fromFields(entityId, fields, createdAt);
        _tasks.add(task);
      }

    } catch (e) {
      _setError('Failed to load tasks: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// Create a new task
  Future<void> createTask(String title, String description) async {
    _setLoading(true);
    _setError(null);

    try {
      const uuid = Uuid();
      final taskId = uuid.v4();
      final now = DateTime.now().millisecondsSinceEpoch;
      final db = _core.database.database;

      // Create entity
      await db.insert('entities', {
        'id': taskId,
        'type_id': 'task',
        'created_at': now,
      });

      // Create events for each field
      if (title.isNotEmpty) {
        await db.insert('events', {
          'id': uuid.v4(),
          'entity_id': taskId,
          'field_id': 'title',
          'value': title,
          'ts': now,
          'node': _core.nodeId,
        });
      }

      if (description.isNotEmpty) {
        await db.insert('events', {
          'id': uuid.v4(),
          'entity_id': taskId,
          'field_id': 'description',
          'value': description,
          'ts': now,
          'node': _core.nodeId,
        });
      }

      // Set initial done state to false
      await db.insert('events', {
        'id': uuid.v4(),
        'entity_id': taskId,
        'field_id': 'done',
        'value': 'false',
        'ts': now,
        'node': _core.nodeId,
      });

      // Reload tasks
      await _loadTasks();

      // Trigger sync
      await _core.sync.syncNow();

    } catch (e) {
      _setError('Failed to create task: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// Complete a task
  Future<void> completeTask(String taskId) async {
    _setLoading(true);
    _setError(null);

    try {
      const uuid = Uuid();
      final now = DateTime.now().millisecondsSinceEpoch;
      final db = _core.database.database;

      // Create event to mark task as done
      await db.insert('events', {
        'id': uuid.v4(),
        'entity_id': taskId,
        'field_id': 'done',
        'value': 'true',
        'ts': now,
        'node': _core.nodeId,
      });

      // Reload tasks
      await _loadTasks();

      // Trigger sync
      await _core.sync.syncNow();

    } catch (e) {
      _setError('Failed to complete task: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// Refresh tasks and sync
  Future<void> refresh() async {
    await _core.sync.syncNow();
    await _loadTasks();
  }
  

  
  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  /// Set error message
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }
  

}
